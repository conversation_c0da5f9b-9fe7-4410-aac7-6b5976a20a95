<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useKanbanStore } from '@/stores/kanbanStore';
import { useSpacesStore } from '@/stores/spacesStore';
import {
	Menu as IconMenu,
	DataAnalysis,
	FolderAdd
} from '@element-plus/icons-vue';

defineProps({
	menuClosed: Boolean,
});

const route = useRoute();
const router = useRouter();
const kanbanStore = useKanbanStore();
const spacesStore = useSpacesStore();
const currentSpaceId = computed(() => {
    // Get the current space ID from the route params
    const routeSpaceId = route.params.spaceId as string;
    return routeSpaceId || spacesStore.currentSpaceId;
});

// Get projects from the kanbanStore
const projects = computed(() => kanbanStore.projects);
const showNewProjectDialog = ref(false);
const newProjectName = ref('');
const newProjectDescription = ref('');

async function createNewProject() {
	if (!newProjectName.value.trim()) return;
	await kanbanStore.createProject(newProjectName.value, newProjectDescription.value);
	showNewProjectDialog.value = false;
	newProjectName.value = '';
	newProjectDescription.value = '';
	router.push(`/${currentSpaceId.value}/kanban/${kanbanStore.currentProjectId}`);
}
</script>

<template>
    <el-menu-item :index="`/${currentSpaceId}`">
        <el-icon>
            <DataAnalysis />
        </el-icon>
        <template #title>Dashboard</template>
    </el-menu-item>

    <!-- Kanban Projects Submenu -->
    <el-sub-menu index="kanban">
        <template #title>
            <el-icon><icon-menu /></el-icon>
            <span>Kanban Projects</span>
        </template>

        <el-menu-item v-for="project in projects" :key="project.id" :index="`/${currentSpaceId}/kanban/${project.id}`">
            {{ project.name }}
        </el-menu-item>

        <el-menu-item @click="showNewProjectDialog = true" index=".">
            <el-icon><folder-add /></el-icon>
            <span>New Project</span>
        </el-menu-item>
    </el-sub-menu>

	<!-- New Project Dialog -->
	<el-dialog v-model="showNewProjectDialog" title="Create New Project" width="30%">
		<el-form label-width="120px">
			<el-form-item label="Project Name" required>
				<el-input v-model="newProjectName" placeholder="Enter project name" />
			</el-form-item>
			<el-form-item label="Description">
				<el-input v-model="newProjectDescription" type="textarea" :rows="3" placeholder="Enter project description" />
			</el-form-item>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="showNewProjectDialog = false">Cancel</el-button>
				<el-button type="primary" @click="createNewProject">Create</el-button>
			</span>
		</template>
	</el-dialog>
</template>
